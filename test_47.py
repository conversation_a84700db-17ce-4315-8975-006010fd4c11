import numpy as np
from importlib import import_module

# Import the gradient_descent function
module = import_module('47')
gradient_descent = module.gradient_descent

# Sample data
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
y = np.array([2, 3, 4, 5])

# Parameters
learning_rate = 0.01
n_iterations = 1000
batch_size = 2

# Initialize weights
weights = np.zeros(X.shape[1])

print("Testing gradient descent methods...")

# Test Batch Gradient Descent
print("\n1. Batch Gradient Descent:")
final_weights_batch = gradient_descent(X, y, weights.copy(), learning_rate, n_iterations, method='batch')
print(f"Final weights: {final_weights_batch}")

# Test Stochastic Gradient Descent
print("\n2. Stochastic Gradient Descent:")
final_weights_stochastic = gradient_descent(X, y, weights.copy(), learning_rate, n_iterations, method='stochastic')
print(f"Final weights: {final_weights_stochastic}")

# Test Mini-Batch Gradient Descent
print("\n3. Mini-Batch Gradient Descent:")
final_weights_minibatch = gradient_descent(X, y, weights.copy(), learning_rate, n_iterations, batch_size, method='mini_batch')
print(f"Final weights: {final_weights_minibatch}")

# Verify predictions
print("\nPredictions comparison:")
print("Original y:", y)
print("Batch predictions:", np.dot(X, final_weights_batch))
print("Stochastic predictions:", np.dot(X, final_weights_stochastic))
print("Mini-batch predictions:", np.dot(X, final_weights_minibatch)) 