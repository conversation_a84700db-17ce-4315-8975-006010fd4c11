import numpy as np
from importlib import import_module

# Import the gradient_descent function
module = import_module('47')
gradient_descent = module.gradient_descent

# Test case for mini-batch
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
y = np.array([2, 3, 4, 5])
weights = np.zeros(X.shape[1])
learning_rate = 0.01
n_iterations = 100
batch_size = 2

print("Testing Mini-Batch Gradient Descent:")
print(f"Expected: [1.10334065 0.68329431]")

output = gradient_descent(X, y, weights, learning_rate, n_iterations, batch_size, method='mini_batch')
print(f"Actual:   {output}")

# Test case for stochastic
print("\nTesting Stochastic Gradient Descent:")
print(f"Expected: [1.0507814 0.83659454]")

output_sgd = gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic')
print(f"Actual:   {output_sgd}")
