import numpy as np

def debug_gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic'):
    """Debug version to understand the expected behavior"""
    weights = weights.copy()
    m = len(y)
    
    print(f"Initial weights: {weights}")
    print(f"Data shape: X={X.shape}, y={y.shape}")
    print(f"Learning rate: {learning_rate}, Iterations: {n_iterations}")
    print()
    
    for i in range(min(5, n_iterations)):  # Show first 5 iterations
        if method == 'stochastic':
            idx = i % m
            X_i = X[idx]
            y_i = y[idx]
            
            prediction = np.dot(X_i, weights)
            error = prediction - y_i
            gradient = 2 * error * X_i
            
            print(f"Iteration {i}:")
            print(f"  Sample index: {idx}")
            print(f"  X_i: {X_i}, y_i: {y_i}")
            print(f"  Prediction: {prediction}")
            print(f"  Error: {error}")
            print(f"  Gradient: {gradient}")
            print(f"  Weight update: {learning_rate * gradient}")
            
            weights -= learning_rate * gradient
            print(f"  New weights: {weights}")
            print()
    
    # Continue with remaining iterations without printing
    for i in range(5, n_iterations):
        if method == 'stochastic':
            idx = i % m
            X_i = X[idx]
            y_i = y[idx]
            
            prediction = np.dot(X_i, weights)
            error = prediction - y_i
            gradient = 2 * error * X_i
            weights -= learning_rate * gradient
    
    return weights

# Test with the failing case
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
y = np.array([2, 3, 4, 5])
weights = np.zeros(X.shape[1])
learning_rate = 0.01
n_iterations = 100

print("=== Debug SGD ===")
result = debug_gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic')
print(f"Final result: {result}")
print(f"Expected: [1.0507814 0.83659454]")
