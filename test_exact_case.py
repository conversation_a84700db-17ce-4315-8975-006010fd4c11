import numpy as np

def gradient_descent_v1(X, y, weights, learning_rate, n_iterations, method='stochastic'):
    """Current implementation"""
    weights = weights.copy()
    m = len(y)
    
    for i in range(n_iterations):
        if method == 'stochastic':
            idx = i % m
            X_i = X[idx]
            y_i = y[idx]
            
            prediction = np.dot(X_i, weights)
            error = prediction - y_i
            gradient = 2 * error * X_i
            weights -= learning_rate * gradient
    
    return weights

def gradient_descent_v2(X, y, weights, learning_rate, n_iterations, method='stochastic'):
    """Alternative implementation - maybe different gradient calculation"""
    weights = weights.copy()
    m = len(y)
    
    for i in range(n_iterations):
        if method == 'stochastic':
            idx = i % m
            X_i = X[idx]
            y_i = y[idx]
            
            prediction = np.dot(X_i, weights)
            error = prediction - y_i
            # Try without the factor of 2
            gradient = error * X_i
            weights -= learning_rate * gradient
    
    return weights

def gradient_descent_v3(X, y, weights, learning_rate, n_iterations, method='stochastic'):
    """Alternative implementation - different iteration pattern"""
    weights = weights.copy()
    m = len(y)
    
    for epoch in range(n_iterations // m):
        for idx in range(m):
            X_i = X[idx]
            y_i = y[idx]
            
            prediction = np.dot(X_i, weights)
            error = prediction - y_i
            gradient = 2 * error * X_i
            weights -= learning_rate * gradient
    
    # Handle remaining iterations
    remaining = n_iterations % m
    for idx in range(remaining):
        X_i = X[idx]
        y_i = y[idx]
        
        prediction = np.dot(X_i, weights)
        error = prediction - y_i
        gradient = 2 * error * X_i
        weights -= learning_rate * gradient
    
    return weights

# Test case
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
y = np.array([2, 3, 4, 5])
weights = np.zeros(X.shape[1])
learning_rate = 0.01
n_iterations = 100

print("Expected: [1.0507814 0.83659454]")
print()

result1 = gradient_descent_v1(X, y, weights, learning_rate, n_iterations)
print(f"V1 (current): {result1}")

result2 = gradient_descent_v2(X, y, weights, learning_rate, n_iterations)
print(f"V2 (no factor 2): {result2}")

result3 = gradient_descent_v3(X, y, weights, learning_rate, n_iterations)
print(f"V3 (epoch-based): {result3}")
