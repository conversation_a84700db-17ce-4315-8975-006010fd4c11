import numpy as np
from importlib import import_module

# Import the gradient_descent function
module = import_module('47')
gradient_descent = module.gradient_descent

# Sample data from docstring
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
y = np.array([2, 3, 4, 5])

# Parameters
learning_rate = 0.01
n_iterations = 1000
batch_size = 2

# Initialize weights
weights = np.zeros(X.shape[1])

print("Testing with docstring example:")
print("X:", X)
print("y:", y)
print("Initial weights:", weights)
print("Learning rate:", learning_rate)
print("Iterations:", n_iterations)
print("Batch size:", batch_size)
print()

# Test Batch Gradient Descent
final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='batch')
print("Batch Gradient Descent result:", final_weights)

# Test Stochastic Gradient Descent
final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic')
print("Stochastic Gradient Descent result:", final_weights)

# Test Mini-Batch Gradient Descent
final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, batch_size, method='mini_batch')
print("Mini-Batch Gradient Descent result:", final_weights)
